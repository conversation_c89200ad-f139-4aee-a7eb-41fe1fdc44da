# AI Navigator Scrapers

A streamlined web scraping and data enrichment system for collecting AI tool information from various online directories and populating the AI Navigator database.

## 🚀 Features

- **Traditional Scraping**: Scrapy-based spiders for collecting raw data from AI tool directories
- **Enhanced Processing**: AI-powered data enrichment with logo extraction and content analysis
- **Frontend Interface**: React-based web interface for monitoring and controlling scraping jobs
- **Real-time Monitoring**: Live job status tracking and progress updates
- **Database Integration**: Direct integration with AI Navigator API for data storage
- **Phase 3 Advanced Features**: Structured data extraction, parallel processing, and performance analysis

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [Available Scrapers](#available-scrapers)
- [API Documentation](#api-documentation)
- [Architecture](#architecture)
- [Troubleshooting](#troubleshooting)

## ⚡ Quick Start

```bash
# 1. Clone and setup
git clone https://github.com/columj9/ai-navigator-scrapers.git
cd ai-navigator-scrapers

# 2. Configure environment
cp .env.example .env
# Edit .env with your API keys

# 3. Start the system
./start_working_system.sh

# 4. Open browser
# Frontend: http://localhost:3000
# Backend API: http://localhost:8001
```

## 🔧 Prerequisites

- Python 3.8+
- Node.js 16+
- API Keys:
  - XAI API Key (for AI enhancement)
  - Perplexity API Key (for content analysis)
  - AI Navigator admin credentials

## 📦 Installation

### 1. Clone the Repository

```bash
git clone https://github.com/columj9/ai-navigator-scrapers.git
cd ai-navigator-scrapers
```

### 2. Set Up Python Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install Python dependencies
pip install -r requirements.txt
```

### 3. Set Up Frontend

```bash
cd frontend
npm install
cd ..
```

## ⚙️ Configuration

### 1. Environment Variables

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your actual values:

```env
# API Keys (Required)
XAI_API_KEY=your_xai_api_key_here
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# AI Navigator Backend (Required)
AI_NAVIGATOR_BASE_URL=https://ai-nav.onrender.com
AI_NAVIGATOR_ADMIN_EMAIL=<EMAIL>
AI_NAVIGATOR_ADMIN_PASSWORD=your_secure_password

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8001
CORS_ORIGINS=http://localhost:3000

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
```

### 2. Required API Keys

#### XAI API Key
1. Visit [XAI Console](https://console.x.ai/)
2. Create an account and generate an API key
3. Add to your `.env` file

#### Perplexity API Key
1. Visit [Perplexity API](https://www.perplexity.ai/settings/api)
2. Create an account and generate an API key
3. Add to your `.env` file

#### AI Navigator Credentials
1. Contact your AI Navigator administrator for credentials
2. Add admin email and password to your `.env` file

## 🚀 Usage

### Starting the System

#### Option 1: Use the Startup Script (Recommended)

```bash
./start_working_system.sh
```

This will automatically:
- Start the backend server on `http://localhost:8001`
- Start the frontend on `http://localhost:3000`
- Handle port conflicts and dependencies

#### Option 2: Manual Startup

```bash
# Terminal 1: Start Backend
python backend/working_server.py

# Terminal 2: Start Frontend
cd frontend
npm start
```

### Using the Web Interface

1. **Open your browser** to `http://localhost:3000`

2. **Dashboard Tab**: View system status and service health

3. **Enhanced Tab**:
   - Add tools manually or use the default examples
   - Configure processing options (parallel processing, Phase 3 features)
   - Click "Start Enhanced Processing" to run AI-powered enhancement

4. **Scraping Tab**:
   - Select a spider from the dropdown
   - Set max items to scrape
   - Click "Start Scraping" for traditional scraping
   - Use "Enhance & Save to DB" to process traditional results

5. **Monitor Progress**: Real-time updates show job status and completion

## 🕷️ Available Scrapers

- **futuretools_complete**: Comprehensive FutureTools.io scraper
- **futuretools_highvolume**: Fast FutureTools.io scraper
- **toolify_spider**: Toolify.ai scraper
- **taaft**: TheresAnAIForThat.com scraper

## 📚 API Documentation

### Core Endpoints

#### System Health
```
GET /api/capabilities          # Available features
GET /api/test-services        # Service status
GET /api/spiders             # Available scrapers
```

#### Traditional Scraping
```
POST /api/start-scraping
Content-Type: application/json

{
  "spider_name": "futuretools_complete",
  "max_items": 50
}
```

#### Enhanced Processing
```
POST /api/start-enhanced-scraping
Content-Type: application/json

{
  "tools": [
    {"name": "ChatGPT", "url": "https://chat.openai.com"},
    {"name": "Notion", "url": "https://www.notion.so"}
  ],
  "use_parallel": true,
  "use_phase3": true
}
```

#### Job Monitoring
```
GET /api/status               # Overall system status
GET /api/jobs                # List all jobs
GET /api/job-status/{job_id} # Specific job status
```

### Response Format

```json
{
  "success": true,
  "job_id": "enhanced_1234567890",
  "message": "Job started successfully",
  "total_tools": 2,
  "processing_mode": "parallel",
  "phase3_enabled": true
}
```

## 🏗️ Architecture

### Project Structure

```
ai-navigator-scrapers/
├── backend/                    # Flask backend server
│   ├── working_server.py      # Main API server
│   ├── enhanced_scraper.py    # Web scraping with AI enhancement
│   └── requirements.txt       # Backend dependencies
├── frontend/                   # React frontend application
│   ├── src/App.js            # Main React component
│   ├── package.json          # Frontend dependencies
│   └── public/               # Static assets
├── ai-navigator-scrapers/      # Scrapy project
│   └── ainav_scrapers/
│       ├── spiders/          # Active scrapers only
│       │   ├── futuretools_complete.py
│       │   ├── futuretools_highvolume.py
│       │   ├── toolify_spider.py
│       │   └── taaft.py
│       ├── items.py          # Data models
│       ├── pipelines.py      # Data processing
│       └── settings.py       # Scrapy configuration
├── shared/                     # Shared utilities
├── output/                     # Scraping output files
├── config.py                   # Configuration management
├── ai_navigator_client.py      # AI Navigator API client
├── requirements.txt            # Main dependencies
└── start_working_system.sh     # System startup script
```

### Data Flow

1. **Traditional Scraping**: Scrapy spiders collect raw data from AI tool directories
2. **Enhanced Processing**: AI-powered enhancement with logo extraction and content analysis
3. **Database Integration**: Direct submission to AI Navigator API
4. **Real-time Monitoring**: Frontend displays live job progress and results

### Key Components

- **Flask Backend**: RESTful API server handling all frontend requests
- **React Frontend**: Web interface for job management and monitoring
- **Scrapy Spiders**: Specialized scrapers for different AI tool directories
- **Enhanced Scraper**: AI-powered web scraping with content analysis
- **AI Navigator Client**: Secure API integration with JWT authentication

## 🔧 Troubleshooting

### Common Issues

#### 1. System Won't Start

**Problem**: `start_working_system.sh` fails

**Solution**:
```bash
# Check if ports are in use
lsof -i :3000
lsof -i :8001

# Kill existing processes
pkill -f "working_server.py"
pkill -f "react-scripts"

# Try starting again
./start_working_system.sh
```

#### 2. Frontend Can't Connect to Backend

**Problem**: API calls fail from frontend

**Solution**:
- Verify backend is running on `http://localhost:8001`
- Check browser console for CORS errors
- Ensure `REACT_APP_BACKEND_URL` is not set or is empty

#### 3. API Key Errors

**Problem**: `XAI_API_KEY is required` or similar errors

**Solution**:
- Verify API keys are set in `.env` file
- Check API key validity and quotas
- Ensure no extra spaces or quotes in `.env` file

#### 4. Scraping Failures

**Problem**: Scrapers fail to extract data

**Solution**:
- Check if target websites have changed structure
- Verify robots.txt compliance
- Check for IP blocking or rate limiting

### Debug Mode

Enable debug logging:

```env
LOG_LEVEL=DEBUG
DEBUG=true
```

Check backend logs:
```bash
# View backend terminal output
# Or check backend/backend.log if it exists
```

### Testing Individual Components

```bash
# Test configuration
python -c "from config import config; print('Config loaded successfully')"

# Test AI Navigator connection
python -c "from ai_navigator_client import AINavigatorClient; client = AINavigatorClient(); print('Connected!')"

# Test backend API
curl http://localhost:8001/api/capabilities
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Scrapy](https://scrapy.org/) - Web scraping framework
- [Flask](https://flask.palletsprojects.com/) - Backend web framework
- [React](https://reactjs.org/) - Frontend framework
- [XAI](https://x.ai/) - AI API for data enrichment
- [Perplexity](https://www.perplexity.ai/) - AI API for content analysis

## 📞 Support

For support and questions:

- Create an issue on GitHub
- Check the [troubleshooting section](#troubleshooting)
- Review the [API documentation](#api-documentation)

---

**Note**: This project is designed to work with the AI Navigator platform. Ensure you have proper access and credentials before setting up the scrapers.